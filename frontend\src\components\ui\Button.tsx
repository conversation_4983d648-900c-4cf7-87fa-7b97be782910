import React from 'react';
import { <PERSON>ton as Ant<PERSON>utton, ButtonProps as AntButtonProps } from 'antd';
import styled, { css } from 'styled-components';
import { theme } from '../../styles/theme';

// 扩展按钮变体
export type ButtonVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'gradient' | 'ghost' | 'link';
export type ButtonSize = 'small' | 'medium' | 'large';

interface CustomButtonProps extends Omit<AntButtonProps, 'variant' | 'size'> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  gradient?: keyof typeof theme.colors.gradients;
  fullWidth?: boolean;
  rounded?: boolean;
  elevated?: boolean;
}

const StyledButton = styled(AntButton)<CustomButtonProps>`
  border: none;
  font-weight: ${theme.typography.fontWeight.medium};
  transition: all ${theme.animation.duration.base} ${theme.animation.easing.easeInOut};
  position: relative;
  overflow: hidden;
  
  // 基础样式
  ${({ size }) => {
    switch (size) {
      case 'small':
        return css`
          height: 32px;
          padding: 0 12px;
          font-size: ${theme.typography.fontSize.sm};
          border-radius: ${theme.borderRadius.sm};
        `;
      case 'large':
        return css`
          height: 48px;
          padding: 0 24px;
          font-size: ${theme.typography.fontSize.lg};
          border-radius: ${theme.borderRadius.lg};
        `;
      default:
        return css`
          height: 40px;
          padding: 0 16px;
          font-size: ${theme.typography.fontSize.base};
          border-radius: ${theme.borderRadius.md};
        `;
    }
  }}

  // 变体样式
  ${({ variant, gradient }) => {
    switch (variant) {
      case 'primary':
        return css`
          background: ${theme.colors.primary[500]};
          color: white;
          
          &:hover {
            background: ${theme.colors.primary[600]};
            transform: translateY(-1px);
            box-shadow: ${theme.boxShadow.md};
          }
          
          &:active {
            transform: translateY(0);
          }
        `;
        
      case 'secondary':
        return css`
          background: ${theme.colors.secondary[500]};
          color: white;
          
          &:hover {
            background: ${theme.colors.secondary[600]};
            transform: translateY(-1px);
            box-shadow: ${theme.boxShadow.md};
          }
        `;
        
      case 'success':
        return css`
          background: ${theme.colors.success};
          color: white;
          
          &:hover {
            background: #45b049;
            transform: translateY(-1px);
            box-shadow: ${theme.boxShadow.md};
          }
        `;
        
      case 'warning':
        return css`
          background: ${theme.colors.warning};
          color: white;
          
          &:hover {
            background: #e6940b;
            transform: translateY(-1px);
            box-shadow: ${theme.boxShadow.md};
          }
        `;
        
      case 'error':
        return css`
          background: ${theme.colors.error};
          color: white;
          
          &:hover {
            background: #d9363e;
            transform: translateY(-1px);
            box-shadow: ${theme.boxShadow.md};
          }
        `;
        
      case 'gradient':
        const gradientColor = gradient ? theme.colors.gradients[gradient] : theme.colors.gradients.primary;
        return css`
          background: ${gradientColor};
          color: white;
          border: none;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: ${theme.boxShadow.lg};
            filter: brightness(1.1);
          }
          
          &:active {
            transform: translateY(-1px);
          }
        `;
        
      case 'ghost':
        return css`
          background: transparent;
          border: 1px solid ${theme.colors.primary[500]};
          color: ${theme.colors.primary[500]};
          
          &:hover {
            background: ${theme.colors.primary[50]};
            border-color: ${theme.colors.primary[600]};
            color: ${theme.colors.primary[600]};
            transform: translateY(-1px);
          }
        `;
        
      case 'link':
        return css`
          background: transparent;
          color: ${theme.colors.primary[500]};
          padding: 0;
          height: auto;
          
          &:hover {
            color: ${theme.colors.primary[600]};
            text-decoration: underline;
          }
        `;
        
      default:
        return css`
          background: ${theme.colors.gray[100]};
          color: ${theme.colors.textPrimary};
          
          &:hover {
            background: ${theme.colors.gray[200]};
            transform: translateY(-1px);
            box-shadow: ${theme.boxShadow.sm};
          }
        `;
    }
  }}

  // 全宽样式
  ${({ fullWidth }) => fullWidth && css`
    width: 100%;
  `}

  // 圆角样式
  ${({ rounded }) => rounded && css`
    border-radius: ${theme.borderRadius.full};
  `}

  // 阴影样式
  ${({ elevated }) => elevated && css`
    box-shadow: ${theme.boxShadow.base};
    
    &:hover {
      box-shadow: ${theme.boxShadow.lg};
    }
  `}

  // 禁用状态
  &:disabled {
    background: ${theme.colors.gray[200]} !important;
    color: ${theme.colors.textDisabled} !important;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
    
    &:hover {
      background: ${theme.colors.gray[200]} !important;
      color: ${theme.colors.textDisabled} !important;
      transform: none !important;
      box-shadow: none !important;
    }
  }

  // 加载状态
  &.ant-btn-loading {
    &:hover {
      transform: none;
    }
  }

  // 波纹效果
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  &:active::before {
    width: 300px;
    height: 300px;
  }
`;

const Button: React.FC<CustomButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  gradient,
  fullWidth = false,
  rounded = false,
  elevated = false,
  children,
  ...props
}) => {
  // 映射到 Ant Design 的 type
  const getAntType = () => {
    switch (variant) {
      case 'primary':
        return 'primary';
      case 'ghost':
        return 'default';
      case 'link':
        return 'link';
      default:
        return 'default';
    }
  };

  // 映射到 Ant Design 的 size
  const getAntSize = () => {
    switch (size) {
      case 'small':
        return 'small';
      case 'large':
        return 'large';
      default:
        return 'middle';
    }
  };

  return (
    <StyledButton
      {...props}
      type={getAntType()}
      size={getAntSize()}
      style={{
        ...props.style,
        ...(fullWidth && { width: '100%' })
      }}
    >
      {children}
    </StyledButton>
  );
};

export default Button;
