{"ast": null, "code": "import api from './api';\nexport const ordersService = {\n  // 创建订单\n  async createOrder(data) {\n    const response = await api.post('/orders', data);\n    return response.data;\n  },\n  // 获取用户订单列表\n  async getOrders(params = {}) {\n    const response = await api.get('/orders', {\n      params\n    });\n    return response.data;\n  },\n  // 获取订单详情\n  async getOrder(id) {\n    const response = await api.get(`/orders/${id}`);\n    return response.data;\n  },\n  // 取消订单\n  async cancelOrder(id) {\n    const response = await api.patch(`/orders/${id}/cancel`);\n    return response.data;\n  },\n  // 申请退货\n  async requestReturn(id, reason) {\n    const response = await api.patch(`/orders/${id}/return`, {\n      reason\n    });\n    return response.data;\n  },\n  // 确认收货\n  async confirmDelivery(id) {\n    const response = await api.patch(`/orders/${id}/confirm`);\n    return response.data;\n  },\n  // 确认订单（别名方法，与confirmDelivery功能相同）\n  async confirmOrder(id) {\n    const response = await api.patch(`/orders/${id}/confirm`);\n    return response.data;\n  },\n  // 获取订单统计\n  async getOrderStats() {\n    const response = await api.get('/orders/stats');\n    return response.data;\n  }\n};", "map": {"version": 3, "names": ["api", "ordersService", "createOrder", "data", "response", "post", "getOrders", "params", "get", "getOrder", "id", "cancelOrder", "patch", "requestReturn", "reason", "confirmDelivery", "confirmOrder", "getOrderStats"], "sources": ["D:/claude镜像/收书卖书/frontend/src/services/orders.ts"], "sourcesContent": ["import api from './api';\nimport { ApiResponse, PaginationResponse, Order, OrderForm } from '../types';\n\nexport const ordersService = {\n  // 创建订单\n  async createOrder(data: OrderForm): Promise<ApiResponse<Order>> {\n    const response = await api.post('/orders', data);\n    return response.data;\n  },\n\n  // 获取用户订单列表\n  async getOrders(params: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    start_date?: string;\n    end_date?: string;\n  } = {}): Promise<PaginationResponse<Order>> {\n    const response = await api.get('/orders', { params });\n    return response.data;\n  },\n\n  // 获取订单详情\n  async getOrder(id: string): Promise<ApiResponse<Order>> {\n    const response = await api.get(`/orders/${id}`);\n    return response.data;\n  },\n\n  // 取消订单\n  async cancelOrder(id: string): Promise<ApiResponse> {\n    const response = await api.patch(`/orders/${id}/cancel`);\n    return response.data;\n  },\n\n  // 申请退货\n  async requestReturn(id: string, reason: string): Promise<ApiResponse> {\n    const response = await api.patch(`/orders/${id}/return`, { reason });\n    return response.data;\n  },\n\n  // 确认收货\n  async confirmDelivery(id: string): Promise<ApiResponse> {\n    const response = await api.patch(`/orders/${id}/confirm`);\n    return response.data;\n  },\n\n  // 确认订单（别名方法，与confirmDelivery功能相同）\n  async confirmOrder(id: string): Promise<ApiResponse> {\n    const response = await api.patch(`/orders/${id}/confirm`);\n    return response.data;\n  },\n\n  // 获取订单统计\n  async getOrderStats(): Promise<ApiResponse<{\n    total: number;\n    pending: number;\n    paid: number;\n    delivering: number;\n    delivered: number;\n    cancelled: number;\n  }>> {\n    const response = await api.get('/orders/stats');\n    return response.data;\n  }\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAGvB,OAAO,MAAMC,aAAa,GAAG;EAC3B;EACA,MAAMC,WAAWA,CAACC,IAAe,EAA+B;IAC9D,MAAMC,QAAQ,GAAG,MAAMJ,GAAG,CAACK,IAAI,CAAC,SAAS,EAAEF,IAAI,CAAC;IAChD,OAAOC,QAAQ,CAACD,IAAI;EACtB,CAAC;EAED;EACA,MAAMG,SAASA,CAACC,MAMf,GAAG,CAAC,CAAC,EAAsC;IAC1C,MAAMH,QAAQ,GAAG,MAAMJ,GAAG,CAACQ,GAAG,CAAC,SAAS,EAAE;MAAED;IAAO,CAAC,CAAC;IACrD,OAAOH,QAAQ,CAACD,IAAI;EACtB,CAAC;EAED;EACA,MAAMM,QAAQA,CAACC,EAAU,EAA+B;IACtD,MAAMN,QAAQ,GAAG,MAAMJ,GAAG,CAACQ,GAAG,CAAC,WAAWE,EAAE,EAAE,CAAC;IAC/C,OAAON,QAAQ,CAACD,IAAI;EACtB,CAAC;EAED;EACA,MAAMQ,WAAWA,CAACD,EAAU,EAAwB;IAClD,MAAMN,QAAQ,GAAG,MAAMJ,GAAG,CAACY,KAAK,CAAC,WAAWF,EAAE,SAAS,CAAC;IACxD,OAAON,QAAQ,CAACD,IAAI;EACtB,CAAC;EAED;EACA,MAAMU,aAAaA,CAACH,EAAU,EAAEI,MAAc,EAAwB;IACpE,MAAMV,QAAQ,GAAG,MAAMJ,GAAG,CAACY,KAAK,CAAC,WAAWF,EAAE,SAAS,EAAE;MAAEI;IAAO,CAAC,CAAC;IACpE,OAAOV,QAAQ,CAACD,IAAI;EACtB,CAAC;EAED;EACA,MAAMY,eAAeA,CAACL,EAAU,EAAwB;IACtD,MAAMN,QAAQ,GAAG,MAAMJ,GAAG,CAACY,KAAK,CAAC,WAAWF,EAAE,UAAU,CAAC;IACzD,OAAON,QAAQ,CAACD,IAAI;EACtB,CAAC;EAED;EACA,MAAMa,YAAYA,CAACN,EAAU,EAAwB;IACnD,MAAMN,QAAQ,GAAG,MAAMJ,GAAG,CAACY,KAAK,CAAC,WAAWF,EAAE,UAAU,CAAC;IACzD,OAAON,QAAQ,CAACD,IAAI;EACtB,CAAC;EAED;EACA,MAAMc,aAAaA,CAAA,EAOf;IACF,MAAMb,QAAQ,GAAG,MAAMJ,GAAG,CAACQ,GAAG,CAAC,eAAe,CAAC;IAC/C,OAAOJ,QAAQ,CAACD,IAAI;EACtB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}