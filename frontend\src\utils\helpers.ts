import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import {
  BOOK_CONDITIONS,
  BOOK_STATUSES,
  ORDER_STATUSES,
  PAYMENT_STATUSES,
  USER_STATUSES,
  USER_ROLES
} from './constants';

// 扩展dayjs插件
dayjs.extend(relativeTime);

// 格式化价格
export const formatPrice = (price: number): string => {
  return `¥${price.toFixed(2)}`;
};

// 格式化日期
export const formatDate = (date: string | Date, format = 'YYYY-MM-DD'): string => {
  return dayjs(date).format(format);
};

// 格式化日期时间
export const formatDateTime = (date: string | Date): string => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 格式化相对时间
export const formatRelativeTime = (date: string | Date): string => {
  return dayjs(date).fromNow();
};

// 获取状态标签配置
export const getStatusConfig = (status: string, type: 'book' | 'order' | 'payment' | 'user' | 'role') => {
  let config;
  
  switch (type) {
    case 'book':
      config = BOOK_STATUSES.find(s => s.value === status);
      break;
    case 'order':
      config = ORDER_STATUSES.find(s => s.value === status);
      break;
    case 'payment':
      config = PAYMENT_STATUSES.find(s => s.value === status);
      break;
    case 'user':
      config = USER_STATUSES.find(s => s.value === status);
      break;
    case 'role':
      config = USER_ROLES.find(s => s.value === status);
      break;
    default:
      config = { color: 'default', label: status };
  }
  
  if (!config) {
    config = { color: 'default', label: status };
  }
  
  return { color: config.color, label: config.label };
};

// 获取图书状况标签配置
export const getConditionConfig = (condition: string) => {
  const config = BOOK_CONDITIONS.find(c => c.value === condition);
  if (!config) {
    return { color: 'default', label: condition };
  }
  return { color: config.color, label: config.label };
};

// 计算折扣
export const calculateDiscount = (originalPrice: number, currentPrice: number): number | null => {
  if (!originalPrice || !currentPrice || originalPrice <= currentPrice) {
    return null;
  }
  return Math.round((1 - currentPrice / originalPrice) * 100);
};

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 验证手机号
export const validatePhone = (phone: string): boolean => {
  return /^1[3-9]\d{9}$/.test(phone);
};

// 验证邮箱
export const validateEmail = (email: string): boolean => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};

// 验证用户名
export const validateUsername = (username: string): boolean => {
  return /^[a-zA-Z0-9_]{3,30}$/.test(username);
};

// 生成随机字符串
export const generateRandomString = (length: number): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, wait);
    }
  };
};

// 深拷贝
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  
  return obj;
};

// 获取图片预览URL
export const getImagePreviewUrl = (url?: string): string => {
  if (!url) {
    return '/images/book-placeholder.png';
  }
  
  // 如果是相对路径，添加API基础URL
  if (url.startsWith('/uploads/')) {
    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
    return apiUrl.replace('/api', '') + url;
  }
  
  return url;
};

// 处理API错误
export const handleApiError = (error: any): string => {
  if (error.response) {
    const { status, data } = error.response;
    
    switch (status) {
      case 400:
        return data?.message || '请求参数错误';
      case 401:
        return '登录已过期，请重新登录';
      case 403:
        return '权限不足';
      case 404:
        return '请求的资源不存在';
      case 500:
        return '服务器内部错误';
      default:
        return data?.message || '请求失败';
    }
  } else if (error.request) {
    return '网络连接失败，请检查网络';
  } else {
    return error.message || '未知错误';
  }
};

// 获取库存状态
export const getStockStatus = (stock: number): {
  status: 'normal' | 'low' | 'out';
  text: string;
  color: string;
} => {
  if (stock === 0) {
    return { status: 'out', text: '暂无库存', color: '#f5222d' };
  }
  if (stock <= 5) {
    return { status: 'low', text: `仅剩 ${stock} 本`, color: '#faad14' };
  }
  return { status: 'normal', text: `库存充足 (${stock} 本)`, color: '#52c41a' };
};

// 计算订单统计
export const calculateOrderStats = (orders: any[]) => {
  const stats = {
    total: orders.length,
    pending: 0,
    paid: 0,
    delivering: 0,
    delivered: 0,
    cancelled: 0,
    totalAmount: 0
  };
  
  orders.forEach(order => {
    stats[order.status as keyof typeof stats]++;
    if (order.payment_status === 'paid') {
      stats.totalAmount += order.total_amount;
    }
  });
  
  return stats;
};

// 生成订单号
export const generateOrderNumber = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  
  return `${year}${month}${day}${random}`;
};
