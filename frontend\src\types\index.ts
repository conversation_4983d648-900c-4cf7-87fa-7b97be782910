// 用户相关类型
export interface User {
  id: string;
  username?: string;
  email?: string;
  phone: string;
  role: 'user' | 'admin' | 'super_admin';
  avatar?: string;
  register_type: 'phone' | 'email' | 'wechat' | 'qq';
  phone_verified: boolean;
  email_verified: boolean;
  status: 'active' | 'inactive' | 'banned';
  contact_wechat?: string;
  contact_qq?: string;
  contact_phone_public?: string;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

// 分类相关类型
export interface Category {
  id: string;
  name: string;
  description?: string;
  parent_id?: string;
  sort_order: number;
  is_active: boolean;
  parent?: Category;
  children?: Category[];
  created_at: string;
  updated_at: string;
}

// 图书相关类型
export interface Book {
  id: string;
  isbn?: string;
  title: string;
  author?: string;
  publisher?: string;
  publication_date?: string;
  category_id: string;
  description?: string;
  condition: '全新' | '九成新' | '八成新' | '七成新' | '六成新';
  price: number;
  original_price?: number;
  stock: number;
  cover_image?: string;
  images?: string[];
  status: '上架' | '下架' | '缺货' | '预售';
  views: number;
  sales_count: number;
  created_by: string;
  updated_by?: string;
  category?: Category;
  creator?: User;
  created_at: string;
  updated_at: string;
}

// 订单相关类型
export interface OrderItem {
  id: string;
  order_id: string;
  book_id: string;
  quantity: number;
  price: number;
  subtotal: number;
  book?: Book;
}

export interface Order {
  id: string;
  order_number: string;
  user_id: string;
  total_amount: number;
  status: 'pending' | 'paid' | 'delivering' | 'delivered' | 'return_requested' | 'returned' | 'cancelled';
  payment_method?: 'alipay' | 'wechat' | 'offline';
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_time?: string;
  delivery_method: 'platform' | 'pickup';
  delivery_address?: string;
  delivery_phone?: string;
  delivery_person?: string;
  delivery_person_phone?: string;
  delivery_time?: string;
  return_reason?: string;
  return_status: 'none' | 'requested' | 'approved' | 'rejected' | 'completed';
  return_requested_at?: string;
  notes?: string;
  user?: User;
  items?: OrderItem[];
  created_at: string;
  updated_at: string;
}

// 消息相关类型
export interface Message {
  id: string;
  sender_id: string;
  receiver_id?: string;
  book_id?: string;
  order_id?: string;
  content: string;
  type: 'chat' | 'book_inquiry' | 'order_inquiry' | 'system_notification';
  status: 'active' | 'hidden' | 'deleted';
  read_status: boolean;
  is_admin_reply: boolean;
  parent_message_id?: string;
  sender?: User;
  receiver?: User;
  book?: Book;
  order?: Order;
  created_at: string;
  updated_at: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

export interface PaginationResponse<T = any> {
  success: boolean;
  data: {
    items?: T[];
    books?: T[];
    orders?: T[];
    users?: T[];
    pagination: {
      current_page: number;
      total_pages: number;
      total_items: number;
      items_per_page: number;
    };
  };
}

// 表单相关类型
export interface LoginForm {
  phone: string;
  password: string;
}

export interface RegisterForm {
  phone: string;
  password: string;
  username?: string;
  email?: string;
}

export interface BookForm {
  title: string;
  author?: string;
  publisher?: string;
  isbn?: string;
  category_id: string;
  description?: string;
  condition: '全新' | '九成新' | '八成新' | '七成新' | '六成新';
  price: number;
  original_price?: number;
  stock: number;
  cover_image?: string;
  images?: string[];
}

export interface OrderForm {
  items: Array<{
    book_id: string;
    quantity: number;
  }>;
  delivery_address: string;
  delivery_phone: string;
}

// 购物车相关类型
export interface CartItem {
  book_id: string;
  book: Book;
  quantity: number;
  price: number; // 添加时的价格（用于锁定价格）
  original_price?: number; // 原价（用于显示折扣）
}

// 搜索和筛选类型
export interface BookSearchParams {
  page?: number;
  limit?: number;
  search?: string;
  category_id?: string;
  min_price?: number;
  max_price?: number;
  condition?: string;
  sort_by?: string;
  sort_order?: 'ASC' | 'DESC';
}

// 统计数据类型
export interface UserStats {
  order_stats: Array<{
    status: string;
    count: number;
  }>;
  total_books: number;
  total_spent: number;
}

export interface AdminStats {
  user_stats: Array<{
    status: string;
    count: number;
  }>;
  book_stats: Array<{
    status: string;
    count: number;
  }>;
  order_stats: Array<{
    status: string;
    count: number;
  }>;
  totals: {
    users: number;
    books: number;
    orders: number;
    revenue: number;
  };
  today: {
    users: number;
    orders: number;
    revenue: number;
  };
}
