import React, { useState, useEffect } from 'react';
import {
  List,
  InputNumber,
  Checkbox,
  Space,
  Typography,
  Image,
  Tag,
  Divider,
  Row,
  Col,
  Alert,
  Modal,
  Input,
  message,
  Tooltip,
  Empty
} from 'antd';
import {
  DeleteOutlined,
  HeartOutlined,
  ShoppingOutlined,
  GiftOutlined,
  TruckOutlined,
  ClearOutlined,
  PercentageOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useCartStore } from '../../stores/cartStore';
import { useAuthStore } from '../../stores/authStore';
import { useNavigate } from 'react-router-dom';
import Button from '../ui/Button';
import Card from '../ui/Card';
import EmptyState from '../ui/EmptyState';
import { theme } from '../../styles/theme';

const { Title, Text } = Typography;

const CartContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  
  .cart-header {
    margin-bottom: 24px;
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
      
      .cart-icon {
        font-size: 24px;
        color: #1677ff;
      }
    }
    
    .header-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .batch-actions {
        display: flex;
        gap: 8px;
      }
      
      .cart-stats {
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
  
  .cart-content {
    display: flex;
    gap: 24px;
    
    @media (max-width: 768px) {
      flex-direction: column;
    }
    
    .cart-items {
      flex: 1;
      
      .cart-item {
        padding: 20px;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.3s ease;
        
        &:hover {
          background-color: #fafafa;
        }
        
        &:last-child {
          border-bottom: none;
        }
        
        .item-content {
          display: flex;
          gap: 16px;
          
          .item-checkbox {
            align-self: flex-start;
            margin-top: 8px;
          }
          
          .item-image {
            width: 100px;
            height: 120px;
            border-radius: 8px;
            overflow: hidden;
            flex-shrink: 0;
            
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          
          .item-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            
            .item-title {
              font-size: 16px;
              font-weight: 600;
              color: #262626;
              margin-bottom: 8px;
              line-height: 1.4;
              cursor: pointer;
              
              &:hover {
                color: #1677ff;
              }
            }
            
            .item-meta {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;
              margin-bottom: 12px;
              
              .meta-item {
                font-size: 13px;
                color: #8c8c8c;
              }
            }
            
            .item-price {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 16px;
              
              .current-price {
                font-size: 18px;
                font-weight: 700;
                color: #ff4d4f;
              }
              
              .original-price {
                font-size: 14px;
                color: #8c8c8c;
                text-decoration: line-through;
              }
              
              .discount-tag {
                font-size: 11px;
              }
            }
            
            .item-actions {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-top: auto;
              
              .quantity-control {
                display: flex;
                align-items: center;
                gap: 8px;
                
                .quantity-input {
                  width: 80px;
                  text-align: center;
                }
              }
              
              .action-buttons {
                display: flex;
                gap: 8px;
                
                .action-btn {
                  padding: 4px 8px;
                  border: none;
                  background: none;
                  color: #8c8c8c;
                  cursor: pointer;
                  border-radius: 4px;
                  transition: all 0.3s ease;
                  
                  &:hover {
                    background: #f5f5f5;
                    color: #1677ff;
                  }
                }
              }
            }
          }
        }
        
        .item-promotion {
          margin-top: 12px;
          padding: 8px 12px;
          background: #fff7e6;
          border: 1px solid #ffd591;
          border-radius: 6px;
          font-size: 12px;
          color: #d46b08;
        }
      }
    }
    
    .cart-summary {
      width: 320px;
      
      @media (max-width: 768px) {
        width: 100%;
      }
      
      .summary-card {
        position: sticky;
        top: 24px;
        
        .summary-header {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 16px;
        }
        
        .summary-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          
          .item-label {
            color: #595959;
          }
          
          .item-value {
            font-weight: 500;
            
            &.highlight {
              color: #ff4d4f;
              font-size: 18px;
              font-weight: 700;
            }
          }
        }
        
        .coupon-section {
          margin: 16px 0;
          padding: 12px;
          background: #f6ffed;
          border: 1px solid #b7eb8f;
          border-radius: 6px;
          
          .coupon-input {
            display: flex;
            gap: 8px;
            margin-top: 8px;
          }
        }
        
        .checkout-btn {
          width: 100%;
          height: 48px;
          font-size: 16px;
          font-weight: 600;
          border-radius: 8px;
          background: linear-gradient(135deg, #1677ff, #4096ff);
          border: none;
          
          &:hover {
            background: linear-gradient(135deg, #0958d9, #1677ff);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
          }
          
          &:disabled {
            background: #f5f5f5;
            color: #bfbfbf;
            transform: none;
            box-shadow: none;
          }
        }
        
        .shipping-info {
          margin-top: 16px;
          padding: 12px;
          background: #f0f9ff;
          border-radius: 6px;
          font-size: 12px;
          color: #0958d9;
          
          .shipping-icon {
            margin-right: 6px;
          }
        }
      }
    }
  }
  
  .empty-cart {
    text-align: center;
    padding: 60px 20px;
    
    .empty-icon {
      font-size: 64px;
      color: #d9d9d9;
      margin-bottom: 16px;
    }
    
    .empty-title {
      font-size: 18px;
      color: #595959;
      margin-bottom: 8px;
    }
    
    .empty-description {
      color: #8c8c8c;
      margin-bottom: 24px;
    }
    
    .go-shopping-btn {
      height: 40px;
      padding: 0 24px;
      border-radius: 8px;
    }
  }
`;

interface EnhancedCartProps {
  className?: string;
}

const EnhancedCart: React.FC<EnhancedCartProps> = ({ className }) => {
  const navigate = useNavigate();
  const { items, updateQuantity, removeItem, clearCart, getTotal, getItemCount } = useCartStore();
  const { isAuthenticated } = useAuthStore();
  
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [couponCode, setCouponCode] = useState('');
  const [appliedCoupon, setAppliedCoupon] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // 默认选中所有商品
    setSelectedItems(items.map(item => item.book_id));
  }, [items]);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(items.map(item => item.book_id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, itemId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  const handleQuantityChange = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      handleRemoveItem(itemId);
    } else {
      updateQuantity(itemId, quantity);
    }
  };

  const handleRemoveItem = (itemId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要从购物车中删除这本图书吗？',
      onOk: () => {
        removeItem(itemId);
        setSelectedItems(prev => prev.filter(id => id !== itemId));
        message.success('已从购物车删除');
      }
    });
  };

  const handleClearCart = () => {
    Modal.confirm({
      title: '清空购物车',
      content: '确定要清空购物车中的所有商品吗？',
      onOk: () => {
        clearCart();
        setSelectedItems([]);
        message.success('购物车已清空');
      }
    });
  };

  const handleApplyCoupon = () => {
    if (!couponCode.trim()) {
      message.warning('请输入优惠券代码');
      return;
    }

    setLoading(true);
    // 模拟优惠券验证
    setTimeout(() => {
      if (couponCode === 'SAVE10') {
        setAppliedCoupon({
          code: couponCode,
          discount: 10,
          type: 'percentage'
        });
        message.success('优惠券使用成功');
      } else {
        message.error('优惠券无效或已过期');
      }
      setLoading(false);
    }, 1000);
  };

  const handleCheckout = () => {
    if (!isAuthenticated) {
      message.warning('请先登录');
      navigate('/login');
      return;
    }

    if (selectedItems.length === 0) {
      message.warning('请选择要结算的商品');
      return;
    }

    // 跳转到结算页面
    navigate('/checkout', {
      state: {
        items: items.filter(item => selectedItems.includes(item.book_id)),
        coupon: appliedCoupon
      }
    });
  };

  const calculateSelectedTotal = () => {
    const selectedItemsData = items.filter(item => selectedItems.includes(item.book_id));
    const subtotal = selectedItemsData.reduce((total, item) => total + item.book.price * item.quantity, 0);
    const discount = appliedCoupon ? (appliedCoupon.type === 'percentage' ? subtotal * appliedCoupon.discount / 100 : appliedCoupon.discount) : 0;
    return {
      subtotal,
      discount,
      total: subtotal - discount,
      shipping: subtotal >= 99 ? 0 : 10
    };
  };

  const summary = calculateSelectedTotal();
  const allSelected = selectedItems.length === items.length && items.length > 0;
  const hasSelectedItems = selectedItems.length > 0;

  if (items.length === 0) {
    return (
      <CartContainer className={className}>
        <EmptyState
          variant="no-cart"
          onAction={() => navigate('/books')}
          actionText="去选购"
        />
      </CartContainer>
    );
  }

  return (
    <CartContainer className={className}>
      <div className="cart-header">
        <div className="header-title">
          <ShoppingOutlined className="cart-icon" />
          <Title level={2}>购物车</Title>
        </div>
        
        <div className="header-actions">
          <div className="batch-actions">
            <Checkbox
              checked={allSelected}
              indeterminate={hasSelectedItems && !allSelected}
              onChange={(e) => handleSelectAll(e.target.checked)}
            >
              全选
            </Checkbox>
            <Button
              variant="ghost"
              onClick={handleClearCart}
              disabled={items.length === 0}
              size="small"
            >
              <ClearOutlined /> 清空购物车
            </Button>
          </div>
          
          <div className="cart-stats">
            共 {getItemCount()} 件商品
          </div>
        </div>
      </div>

      <div className="cart-content">
        <Card className="cart-items" bordered={false}>
          <List
            dataSource={items}
            renderItem={(item) => (
              <div key={item.book_id} className="cart-item">
                <div className="item-content">
                  <Checkbox
                    className="item-checkbox"
                    checked={selectedItems.includes(item.book_id)}
                    onChange={(e) => handleSelectItem(item.book_id, e.target.checked)}
                  />

                  <div className="item-image">
                    <Image
                      src={item.book.cover_image || '/images/book-placeholder.png'}
                      alt={item.book.title}
                      preview={false}
                    />
                  </div>

                  <div className="item-info">
                    <div
                      className="item-title"
                      onClick={() => navigate(`/books/${item.book.id}`)}
                    >
                      {item.book.title}
                    </div>
                    
                    <div className="item-meta">
                      <span className="meta-item">作者: {item.book.author || '未知'}</span>
                      <Tag color="blue">现货</Tag>
                    </div>

                    <div className="item-price">
                      <span className="current-price">¥{item.book.price.toFixed(2)}</span>
                      {item.book.original_price && item.book.original_price > item.book.price && (
                        <>
                          <span className="original-price">¥{item.book.original_price.toFixed(2)}</span>
                          <Tag className="discount-tag" color="red">
                            {Math.round((1 - item.book.price / item.book.original_price) * 100)}折
                          </Tag>
                        </>
                      )}
                    </div>
                    
                    <div className="item-actions">
                      <div className="quantity-control">
                        <Button
                          variant="ghost"
                          size="small"
                          onClick={() => handleQuantityChange(item.book_id, item.quantity - 1)}
                          rounded
                        >
                          -
                        </Button>
                        <InputNumber
                          className="quantity-input"
                          size="small"
                          min={1}
                          max={item.book.stock}
                          value={item.quantity}
                          onChange={(value) => handleQuantityChange(item.book_id, value || 1)}
                        />
                        <Button
                          variant="ghost"
                          size="small"
                          onClick={() => handleQuantityChange(item.book_id, item.quantity + 1)}
                          disabled={item.quantity >= item.book.stock}
                          rounded
                        >
                          +
                        </Button>
                      </div>
                      
                      <div className="action-buttons">
                        <Tooltip title="移到收藏夹">
                          <button className="action-btn">
                            <HeartOutlined />
                          </button>
                        </Tooltip>
                        <Tooltip title="删除">
                          <button
                            className="action-btn"
                            onClick={() => handleRemoveItem(item.book_id)}
                          >
                            <DeleteOutlined />
                          </button>
                        </Tooltip>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* 促销信息 */}
                {item.price < (item.original_price || item.price) && (
                  <div className="item-promotion">
                    <GiftOutlined /> 限时特惠，立省 ¥{((item.original_price || item.price) - item.price).toFixed(2)}
                  </div>
                )}
              </div>
            )}
          />
        </Card>

        <Card className="cart-summary summary-card" title="订单摘要">
          <div className="summary-item">
            <span className="item-label">商品小计 ({selectedItems.length}件)</span>
            <span className="item-value">¥{summary.subtotal.toFixed(2)}</span>
          </div>
          
          {appliedCoupon && (
            <div className="summary-item">
              <span className="item-label">优惠券折扣</span>
              <span className="item-value" style={{ color: '#52c41a' }}>
                -¥{summary.discount.toFixed(2)}
              </span>
            </div>
          )}
          
          <div className="summary-item">
            <span className="item-label">运费</span>
            <span className="item-value">
              {summary.shipping === 0 ? '免运费' : `¥${summary.shipping.toFixed(2)}`}
            </span>
          </div>
          
          <Divider />
          
          <div className="summary-item">
            <span className="item-label">合计</span>
            <span className="item-value highlight">
              ¥{(summary.total + summary.shipping).toFixed(2)}
            </span>
          </div>
          
          {/* 优惠券 */}
          <div className="coupon-section">
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <PercentageOutlined />
              <Text strong>优惠券</Text>
            </div>
            {!appliedCoupon ? (
              <div className="coupon-input">
                <Input
                  placeholder="输入优惠券代码"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value)}
                />
                <Button
                  variant="gradient"
                  gradient="primary"
                  loading={loading}
                  onClick={handleApplyCoupon}
                  rounded
                >
                  使用
                </Button>
              </div>
            ) : (
              <div style={{ marginTop: 8 }}>
                <Tag color="green">
                  {appliedCoupon.code} 已使用
                </Tag>
                <Button
                  variant="link"
                  size="small"
                  onClick={() => {
                    setAppliedCoupon(null);
                    setCouponCode('');
                  }}
                >
                  取消
                </Button>
              </div>
            )}
          </div>
          
          <Button
            variant="gradient"
            gradient="primary"
            size="large"
            disabled={!hasSelectedItems}
            onClick={handleCheckout}
            fullWidth
            rounded
            elevated
          >
            结算 ({selectedItems.length})
          </Button>
          
          <div className="shipping-info">
            <TruckOutlined className="shipping-icon" />
            {summary.subtotal >= 99 ? '已享受免运费' : '满99元免运费'}
          </div>
        </Card>
      </div>
    </CartContainer>
  );
};

export default EnhancedCart;
