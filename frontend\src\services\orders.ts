import api from './api';
import { ApiResponse, PaginationResponse, Order, OrderForm } from '../types';

export const ordersService = {
  // 创建订单
  async createOrder(data: OrderForm): Promise<ApiResponse<Order>> {
    const response = await api.post('/orders', data);
    return response.data;
  },

  // 获取用户订单列表
  async getOrders(params: {
    page?: number;
    limit?: number;
    status?: string;
    start_date?: string;
    end_date?: string;
  } = {}): Promise<PaginationResponse<Order>> {
    const response = await api.get('/orders', { params });
    return response.data;
  },

  // 获取订单详情
  async getOrder(id: string): Promise<ApiResponse<Order>> {
    const response = await api.get(`/orders/${id}`);
    return response.data;
  },

  // 取消订单
  async cancelOrder(id: string): Promise<ApiResponse> {
    const response = await api.patch(`/orders/${id}/cancel`);
    return response.data;
  },

  // 申请退货
  async requestReturn(id: string, reason: string): Promise<ApiResponse> {
    const response = await api.patch(`/orders/${id}/return`, { reason });
    return response.data;
  },

  // 确认收货
  async confirmDelivery(id: string): Promise<ApiResponse> {
    const response = await api.patch(`/orders/${id}/confirm`);
    return response.data;
  },

  // 确认订单（别名方法，与confirmDelivery功能相同）
  async confirmOrder(id: string): Promise<ApiResponse> {
    const response = await api.patch(`/orders/${id}/confirm`);
    return response.data;
  },

  // 获取订单统计
  async getOrderStats(): Promise<ApiResponse<{
    total: number;
    pending: number;
    paid: number;
    delivering: number;
    delivered: number;
    cancelled: number;
  }>> {
    const response = await api.get('/orders/stats');
    return response.data;
  }
};
