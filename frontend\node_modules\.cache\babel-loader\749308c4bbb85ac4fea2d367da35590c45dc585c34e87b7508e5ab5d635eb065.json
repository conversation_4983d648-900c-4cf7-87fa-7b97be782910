{"ast": null, "code": "import axios from 'axios';\nimport { handleApiError } from '../utils/errorHandler';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',\n  timeout: 15000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  // 添加认证token\n  const token = localStorage.getItem('token');\n  if (token && config.headers) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n\n  // 添加请求ID用于追踪\n  if (config.headers) {\n    config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  // 记录成功的请求（仅在开发环境）\n  if (process.env.NODE_ENV === 'development') {\n    var _response$config$meth;\n    console.log(`API Success: ${(_response$config$meth = response.config.method) === null || _response$config$meth === void 0 ? void 0 : _response$config$meth.toUpperCase()} ${response.config.url}`, response.data);\n  }\n  return response;\n}, error => {\n  // 记录错误的请求\n  if (process.env.NODE_ENV === 'development') {\n    var _error$config, _error$config$method, _error$config2;\n    console.error(`API Error: ${(_error$config = error.config) === null || _error$config === void 0 ? void 0 : (_error$config$method = _error$config.method) === null || _error$config$method === void 0 ? void 0 : _error$config$method.toUpperCase()} ${(_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.url}`, error);\n  }\n\n  // 使用统一的错误处理器\n  handleApiError(error);\n  return Promise.reject(error);\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "handleApiError", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "Date", "now", "Math", "random", "toString", "substr", "error", "Promise", "reject", "response", "NODE_ENV", "_response$config$meth", "console", "log", "method", "toUpperCase", "url", "data", "_error$config", "_error$config$method", "_error$config2"], "sources": ["D:/claude镜像/收书卖书/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';\nimport { handleApiError } from '../utils/errorHandler';\n\n// 创建axios实例\nconst api: AxiosInstance = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',\n  timeout: 15000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config: InternalAxiosRequestConfig) => {\n    // 添加认证token\n    const token = localStorage.getItem('token');\n    if (token && config.headers) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n\n    // 添加请求ID用于追踪\n    if (config.headers) {\n      config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response: AxiosResponse) => {\n    // 记录成功的请求（仅在开发环境）\n    if (process.env.NODE_ENV === 'development') {\n      console.log(`API Success: ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data);\n    }\n\n    return response;\n  },\n  (error: AxiosError) => {\n    // 记录错误的请求\n    if (process.env.NODE_ENV === 'development') {\n      console.error(`API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, error);\n    }\n\n    // 使用统一的错误处理器\n    handleApiError(error);\n\n    return Promise.reject(error);\n  }\n);\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAgF,OAAO;AACnG,SAASC,cAAc,QAAQ,uBAAuB;;AAEtD;AACA,MAAMC,GAAkB,GAAGF,KAAK,CAACG,MAAM,CAAC;EACtCC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;EACrEC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAkC,IAAK;EACtC;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,IAAID,MAAM,CAACJ,OAAO,EAAE;IAC3BI,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;;EAEA;EACA,IAAID,MAAM,CAACJ,OAAO,EAAE;IAClBI,MAAM,CAACJ,OAAO,CAAC,cAAc,CAAC,GAAG,OAAOS,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACjG;EAEA,OAAOV,MAAM;AACf,CAAC,EACAW,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAtB,GAAG,CAACQ,YAAY,CAACiB,QAAQ,CAACf,GAAG,CAC1Be,QAAuB,IAAK;EAC3B;EACA,IAAItB,OAAO,CAACC,GAAG,CAACsB,QAAQ,KAAK,aAAa,EAAE;IAAA,IAAAC,qBAAA;IAC1CC,OAAO,CAACC,GAAG,CAAC,iBAAAF,qBAAA,GAAgBF,QAAQ,CAACd,MAAM,CAACmB,MAAM,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBI,WAAW,CAAC,CAAC,IAAIN,QAAQ,CAACd,MAAM,CAACqB,GAAG,EAAE,EAAEP,QAAQ,CAACQ,IAAI,CAAC;EAC5G;EAEA,OAAOR,QAAQ;AACjB,CAAC,EACAH,KAAiB,IAAK;EACrB;EACA,IAAInB,OAAO,CAACC,GAAG,CAACsB,QAAQ,KAAK,aAAa,EAAE;IAAA,IAAAQ,aAAA,EAAAC,oBAAA,EAAAC,cAAA;IAC1CR,OAAO,CAACN,KAAK,CAAC,eAAAY,aAAA,GAAcZ,KAAK,CAACX,MAAM,cAAAuB,aAAA,wBAAAC,oBAAA,GAAZD,aAAA,CAAcJ,MAAM,cAAAK,oBAAA,uBAApBA,oBAAA,CAAsBJ,WAAW,CAAC,CAAC,KAAAK,cAAA,GAAId,KAAK,CAACX,MAAM,cAAAyB,cAAA,uBAAZA,cAAA,CAAcJ,GAAG,EAAE,EAAEV,KAAK,CAAC;EAChG;;EAEA;EACAvB,cAAc,CAACuB,KAAK,CAAC;EAErB,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAetB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}