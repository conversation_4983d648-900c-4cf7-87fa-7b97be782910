{"ast": null, "code": "var _jsxFileName = \"D:\\\\claude\\u955C\\u50CF\\\\\\u6536\\u4E66\\u5356\\u4E66\\\\frontend\\\\src\\\\components\\\\ui\\\\Card.tsx\";\nimport React from 'react';\nimport { Card as AntCard } from 'antd';\nimport styled, { css } from 'styled-components';\nimport { theme } from '../../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledCard = styled(AntCard)`\n  border-radius: ${theme.borderRadius.lg};\n  transition: all ${theme.animation.duration.base} ${theme.animation.easing.easeInOut};\n  position: relative;\n  overflow: hidden;\n  \n  // 基础尺寸\n  ${({\n  size\n}) => {\n  switch (size) {\n    case 'small':\n      return css`\n          .ant-card-body {\n            padding: 12px;\n          }\n          .ant-card-head {\n            padding: 0 12px;\n            min-height: 40px;\n          }\n        `;\n    case 'large':\n      return css`\n          .ant-card-body {\n            padding: 32px;\n          }\n          .ant-card-head {\n            padding: 0 32px;\n            min-height: 64px;\n          }\n        `;\n    default:\n      return css`\n          .ant-card-body {\n            padding: 20px;\n          }\n          .ant-card-head {\n            padding: 0 20px;\n            min-height: 56px;\n          }\n        `;\n  }\n}}\n\n  // 变体样式\n  ${({\n  variant,\n  gradient\n}) => {\n  switch (variant) {\n    case 'elevated':\n      return css`\n          border: none;\n          box-shadow: ${theme.boxShadow.lg};\n          \n          &:hover {\n            box-shadow: ${theme.boxShadow.xl};\n            transform: translateY(-4px);\n          }\n        `;\n    case 'outlined':\n      return css`\n          border: 2px solid ${theme.colors.border};\n          box-shadow: none;\n          \n          &:hover {\n            border-color: ${theme.colors.primary[500]};\n            box-shadow: ${theme.boxShadow.md};\n          }\n        `;\n    case 'gradient':\n      const gradientColor = gradient ? theme.colors.gradients[gradient] : theme.colors.gradients.primary;\n      return css`\n          background: ${gradientColor};\n          border: none;\n          color: white;\n          \n          .ant-card-head-title {\n            color: white;\n          }\n          \n          .ant-card-extra {\n            color: white;\n          }\n          \n          &:hover {\n            transform: translateY(-2px);\n            box-shadow: ${theme.boxShadow.xl};\n            filter: brightness(1.05);\n          }\n        `;\n    case 'glass':\n      return css`\n          background: rgba(255, 255, 255, 0.1);\n          backdrop-filter: blur(10px);\n          border: 1px solid rgba(255, 255, 255, 0.2);\n          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n          \n          &:hover {\n            background: rgba(255, 255, 255, 0.15);\n            transform: translateY(-2px);\n          }\n        `;\n    default:\n      return css`\n          border: 1px solid ${theme.colors.borderLight};\n          box-shadow: ${theme.boxShadow.sm};\n          \n          &:hover {\n            box-shadow: ${theme.boxShadow.md};\n          }\n        `;\n  }\n}}\n\n  // 悬停效果\n  ${({\n  hoverable\n}) => hoverable && css`\n    cursor: pointer;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: ${theme.boxShadow.lg};\n    }\n    \n    &:active {\n      transform: translateY(-1px);\n    }\n  `}\n\n  // 动画效果\n  ${({\n  animated\n}) => animated && css`\n    &::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: -100%;\n      width: 100%;\n      height: 100%;\n      background: linear-gradient(\n        90deg,\n        transparent,\n        rgba(255, 255, 255, 0.2),\n        transparent\n      );\n      transition: left 0.5s;\n    }\n    \n    &:hover::before {\n      left: 100%;\n    }\n  `}\n\n  // 发光效果\n  ${({\n  glowing\n}) => glowing && css`\n    &::after {\n      content: '';\n      position: absolute;\n      top: -2px;\n      left: -2px;\n      right: -2px;\n      bottom: -2px;\n      background: ${theme.colors.gradients.primary};\n      border-radius: ${theme.borderRadius.lg};\n      z-index: -1;\n      opacity: 0;\n      transition: opacity ${theme.animation.duration.base};\n    }\n    \n    &:hover::after {\n      opacity: 0.7;\n    }\n  `}\n\n  // 卡片头部样式\n  .ant-card-head {\n    border-bottom: 1px solid ${theme.colors.borderLight};\n    \n    .ant-card-head-title {\n      font-weight: ${theme.typography.fontWeight.semibold};\n      font-size: ${theme.typography.fontSize.lg};\n    }\n  }\n\n  // 卡片内容样式\n  .ant-card-body {\n    position: relative;\n  }\n\n  // 卡片操作区域\n  .ant-card-actions {\n    border-top: 1px solid ${theme.colors.borderLight};\n    \n    > li {\n      margin: 12px 0;\n      \n      &:not(:last-child) {\n        border-right: 1px solid ${theme.colors.borderLight};\n      }\n    }\n  }\n\n  // 加载状态\n  &.ant-card-loading {\n    .ant-card-body {\n      user-select: none;\n    }\n  }\n`;\n_c = StyledCard;\nconst Card = ({\n  variant = 'default',\n  size = 'medium',\n  gradient,\n  hoverable = false,\n  animated = false,\n  glowing = false,\n  children,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(StyledCard, {\n    variant: variant,\n    size: size,\n    gradient: gradient,\n    hoverable: hoverable,\n    animated: animated,\n    glowing: glowing,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 237,\n    columnNumber: 5\n  }, this);\n};\n_c2 = Card;\nexport default Card;\nvar _c, _c2;\n$RefreshReg$(_c, \"StyledCard\");\n$RefreshReg$(_c2, \"Card\");", "map": {"version": 3, "names": ["React", "Card", "AntCard", "styled", "css", "theme", "jsxDEV", "_jsxDEV", "StyledCard", "borderRadius", "lg", "animation", "duration", "base", "easing", "easeInOut", "size", "variant", "gradient", "boxShadow", "xl", "colors", "border", "primary", "md", "gradientColor", "gradients", "borderLight", "sm", "hoverable", "animated", "glowing", "typography", "fontWeight", "semibold", "fontSize", "_c", "children", "props", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "$RefreshReg$"], "sources": ["D:/claude镜像/收书卖书/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card as AntCard, CardProps as AntCardProps } from 'antd';\nimport styled, { css } from 'styled-components';\nimport { theme } from '../../styles/theme';\n\nexport type CardVariant = 'default' | 'elevated' | 'outlined' | 'gradient' | 'glass';\nexport type CardSize = 'small' | 'medium' | 'large';\n\ninterface CustomCardProps extends Omit<AntCardProps, 'size'> {\n  variant?: CardVariant;\n  size?: CardSize;\n  gradient?: keyof typeof theme.colors.gradients;\n  hoverable?: boolean;\n  animated?: boolean;\n  glowing?: boolean;\n}\n\nconst StyledCard = styled(AntCard)<CustomCardProps>`\n  border-radius: ${theme.borderRadius.lg};\n  transition: all ${theme.animation.duration.base} ${theme.animation.easing.easeInOut};\n  position: relative;\n  overflow: hidden;\n  \n  // 基础尺寸\n  ${({ size }) => {\n    switch (size) {\n      case 'small':\n        return css`\n          .ant-card-body {\n            padding: 12px;\n          }\n          .ant-card-head {\n            padding: 0 12px;\n            min-height: 40px;\n          }\n        `;\n      case 'large':\n        return css`\n          .ant-card-body {\n            padding: 32px;\n          }\n          .ant-card-head {\n            padding: 0 32px;\n            min-height: 64px;\n          }\n        `;\n      default:\n        return css`\n          .ant-card-body {\n            padding: 20px;\n          }\n          .ant-card-head {\n            padding: 0 20px;\n            min-height: 56px;\n          }\n        `;\n    }\n  }}\n\n  // 变体样式\n  ${({ variant, gradient }) => {\n    switch (variant) {\n      case 'elevated':\n        return css`\n          border: none;\n          box-shadow: ${theme.boxShadow.lg};\n          \n          &:hover {\n            box-shadow: ${theme.boxShadow.xl};\n            transform: translateY(-4px);\n          }\n        `;\n        \n      case 'outlined':\n        return css`\n          border: 2px solid ${theme.colors.border};\n          box-shadow: none;\n          \n          &:hover {\n            border-color: ${theme.colors.primary[500]};\n            box-shadow: ${theme.boxShadow.md};\n          }\n        `;\n        \n      case 'gradient':\n        const gradientColor = gradient ? theme.colors.gradients[gradient] : theme.colors.gradients.primary;\n        return css`\n          background: ${gradientColor};\n          border: none;\n          color: white;\n          \n          .ant-card-head-title {\n            color: white;\n          }\n          \n          .ant-card-extra {\n            color: white;\n          }\n          \n          &:hover {\n            transform: translateY(-2px);\n            box-shadow: ${theme.boxShadow.xl};\n            filter: brightness(1.05);\n          }\n        `;\n        \n      case 'glass':\n        return css`\n          background: rgba(255, 255, 255, 0.1);\n          backdrop-filter: blur(10px);\n          border: 1px solid rgba(255, 255, 255, 0.2);\n          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n          \n          &:hover {\n            background: rgba(255, 255, 255, 0.15);\n            transform: translateY(-2px);\n          }\n        `;\n        \n      default:\n        return css`\n          border: 1px solid ${theme.colors.borderLight};\n          box-shadow: ${theme.boxShadow.sm};\n          \n          &:hover {\n            box-shadow: ${theme.boxShadow.md};\n          }\n        `;\n    }\n  }}\n\n  // 悬停效果\n  ${({ hoverable }) => hoverable && css`\n    cursor: pointer;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: ${theme.boxShadow.lg};\n    }\n    \n    &:active {\n      transform: translateY(-1px);\n    }\n  `}\n\n  // 动画效果\n  ${({ animated }) => animated && css`\n    &::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: -100%;\n      width: 100%;\n      height: 100%;\n      background: linear-gradient(\n        90deg,\n        transparent,\n        rgba(255, 255, 255, 0.2),\n        transparent\n      );\n      transition: left 0.5s;\n    }\n    \n    &:hover::before {\n      left: 100%;\n    }\n  `}\n\n  // 发光效果\n  ${({ glowing }) => glowing && css`\n    &::after {\n      content: '';\n      position: absolute;\n      top: -2px;\n      left: -2px;\n      right: -2px;\n      bottom: -2px;\n      background: ${theme.colors.gradients.primary};\n      border-radius: ${theme.borderRadius.lg};\n      z-index: -1;\n      opacity: 0;\n      transition: opacity ${theme.animation.duration.base};\n    }\n    \n    &:hover::after {\n      opacity: 0.7;\n    }\n  `}\n\n  // 卡片头部样式\n  .ant-card-head {\n    border-bottom: 1px solid ${theme.colors.borderLight};\n    \n    .ant-card-head-title {\n      font-weight: ${theme.typography.fontWeight.semibold};\n      font-size: ${theme.typography.fontSize.lg};\n    }\n  }\n\n  // 卡片内容样式\n  .ant-card-body {\n    position: relative;\n  }\n\n  // 卡片操作区域\n  .ant-card-actions {\n    border-top: 1px solid ${theme.colors.borderLight};\n    \n    > li {\n      margin: 12px 0;\n      \n      &:not(:last-child) {\n        border-right: 1px solid ${theme.colors.borderLight};\n      }\n    }\n  }\n\n  // 加载状态\n  &.ant-card-loading {\n    .ant-card-body {\n      user-select: none;\n    }\n  }\n`;\n\nconst Card: React.FC<CustomCardProps> = ({\n  variant = 'default',\n  size = 'medium',\n  gradient,\n  hoverable = false,\n  animated = false,\n  glowing = false,\n  children,\n  ...props\n}) => {\n  return (\n    <StyledCard\n      variant={variant}\n      size={size}\n      gradient={gradient}\n      hoverable={hoverable}\n      animated={animated}\n      glowing={glowing}\n      {...props}\n    >\n      {children}\n    </StyledCard>\n  );\n};\n\nexport default Card;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,IAAIC,OAAO,QAAmC,MAAM;AACjE,OAAOC,MAAM,IAAIC,GAAG,QAAQ,mBAAmB;AAC/C,SAASC,KAAK,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc3C,MAAMC,UAAU,GAAGL,MAAM,CAACD,OAAO,CAAkB;AACnD,mBAAmBG,KAAK,CAACI,YAAY,CAACC,EAAE;AACxC,oBAAoBL,KAAK,CAACM,SAAS,CAACC,QAAQ,CAACC,IAAI,IAAIR,KAAK,CAACM,SAAS,CAACG,MAAM,CAACC,SAAS;AACrF;AACA;AACA;AACA;AACA,IAAI,CAAC;EAAEC;AAAK,CAAC,KAAK;EACd,QAAQA,IAAI;IACV,KAAK,OAAO;MACV,OAAOZ,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;IACH,KAAK,OAAO;MACV,OAAOA,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;IACH;MACE,OAAOA,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH;AACA;AACA,IAAI,CAAC;EAAEa,OAAO;EAAEC;AAAS,CAAC,KAAK;EAC3B,QAAQD,OAAO;IACb,KAAK,UAAU;MACb,OAAOb,GAAG;AAClB;AACA,wBAAwBC,KAAK,CAACc,SAAS,CAACT,EAAE;AAC1C;AACA;AACA,0BAA0BL,KAAK,CAACc,SAAS,CAACC,EAAE;AAC5C;AACA;AACA,SAAS;IAEH,KAAK,UAAU;MACb,OAAOhB,GAAG;AAClB,8BAA8BC,KAAK,CAACgB,MAAM,CAACC,MAAM;AACjD;AACA;AACA;AACA,4BAA4BjB,KAAK,CAACgB,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;AACrD,0BAA0BlB,KAAK,CAACc,SAAS,CAACK,EAAE;AAC5C;AACA,SAAS;IAEH,KAAK,UAAU;MACb,MAAMC,aAAa,GAAGP,QAAQ,GAAGb,KAAK,CAACgB,MAAM,CAACK,SAAS,CAACR,QAAQ,CAAC,GAAGb,KAAK,CAACgB,MAAM,CAACK,SAAS,CAACH,OAAO;MAClG,OAAOnB,GAAG;AAClB,wBAAwBqB,aAAa;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0BpB,KAAK,CAACc,SAAS,CAACC,EAAE;AAC5C;AACA;AACA,SAAS;IAEH,KAAK,OAAO;MACV,OAAOhB,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;IAEH;MACE,OAAOA,GAAG;AAClB,8BAA8BC,KAAK,CAACgB,MAAM,CAACM,WAAW;AACtD,wBAAwBtB,KAAK,CAACc,SAAS,CAACS,EAAE;AAC1C;AACA;AACA,0BAA0BvB,KAAK,CAACc,SAAS,CAACK,EAAE;AAC5C;AACA,SAAS;EACL;AACF,CAAC;AACH;AACA;AACA,IAAI,CAAC;EAAEK;AAAU,CAAC,KAAKA,SAAS,IAAIzB,GAAG;AACvC;AACA;AACA;AACA;AACA,oBAAoBC,KAAK,CAACc,SAAS,CAACT,EAAE;AACtC;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,IAAI,CAAC;EAAEoB;AAAS,CAAC,KAAKA,QAAQ,IAAI1B,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,IAAI,CAAC;EAAE2B;AAAQ,CAAC,KAAKA,OAAO,IAAI3B,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoBC,KAAK,CAACgB,MAAM,CAACK,SAAS,CAACH,OAAO;AAClD,uBAAuBlB,KAAK,CAACI,YAAY,CAACC,EAAE;AAC5C;AACA;AACA,4BAA4BL,KAAK,CAACM,SAAS,CAACC,QAAQ,CAACC,IAAI;AACzD;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,+BAA+BR,KAAK,CAACgB,MAAM,CAACM,WAAW;AACvD;AACA;AACA,qBAAqBtB,KAAK,CAAC2B,UAAU,CAACC,UAAU,CAACC,QAAQ;AACzD,mBAAmB7B,KAAK,CAAC2B,UAAU,CAACG,QAAQ,CAACzB,EAAE;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4BL,KAAK,CAACgB,MAAM,CAACM,WAAW;AACpD;AACA;AACA;AACA;AACA;AACA,kCAAkCtB,KAAK,CAACgB,MAAM,CAACM,WAAW;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACS,EAAA,GA9MI5B,UAAU;AAgNhB,MAAMP,IAA+B,GAAGA,CAAC;EACvCgB,OAAO,GAAG,SAAS;EACnBD,IAAI,GAAG,QAAQ;EACfE,QAAQ;EACRW,SAAS,GAAG,KAAK;EACjBC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfM,QAAQ;EACR,GAAGC;AACL,CAAC,KAAK;EACJ,oBACE/B,OAAA,CAACC,UAAU;IACTS,OAAO,EAAEA,OAAQ;IACjBD,IAAI,EAAEA,IAAK;IACXE,QAAQ,EAAEA,QAAS;IACnBW,SAAS,EAAEA,SAAU;IACrBC,QAAQ,EAAEA,QAAS;IACnBC,OAAO,EAAEA,OAAQ;IAAA,GACbO,KAAK;IAAAD,QAAA,EAERA;EAAQ;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEjB,CAAC;AAACC,GAAA,GAvBI1C,IAA+B;AAyBrC,eAAeA,IAAI;AAAC,IAAAmC,EAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAR,EAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}