import React from 'react';
import { Card as AntCard, CardProps as AntCardProps } from 'antd';
import styled, { css } from 'styled-components';
import { theme } from '../../styles/theme';

export type CardVariant = 'default' | 'elevated' | 'outlined' | 'gradient' | 'glass';
export type CardSize = 'small' | 'medium' | 'large';

interface CustomCardProps extends Omit<AntCardProps, 'size'> {
  variant?: CardVariant;
  size?: CardSize;
  gradient?: keyof typeof theme.colors.gradients;
  hoverable?: boolean;
  animated?: boolean;
  glowing?: boolean;
}

const StyledCard = styled(AntCard)<CustomCardProps>`
  border-radius: ${theme.borderRadius.lg};
  transition: all ${theme.animation.duration.base} ${theme.animation.easing.easeInOut};
  position: relative;
  overflow: hidden;
  
  // 基础尺寸
  ${({ size }) => {
    switch (size) {
      case 'small':
        return css`
          .ant-card-body {
            padding: 12px;
          }
          .ant-card-head {
            padding: 0 12px;
            min-height: 40px;
          }
        `;
      case 'large':
        return css`
          .ant-card-body {
            padding: 32px;
          }
          .ant-card-head {
            padding: 0 32px;
            min-height: 64px;
          }
        `;
      default:
        return css`
          .ant-card-body {
            padding: 20px;
          }
          .ant-card-head {
            padding: 0 20px;
            min-height: 56px;
          }
        `;
    }
  }}

  // 变体样式
  ${({ variant, gradient }) => {
    switch (variant) {
      case 'elevated':
        return css`
          border: none;
          box-shadow: ${theme.boxShadow.lg};
          
          &:hover {
            box-shadow: ${theme.boxShadow.xl};
            transform: translateY(-4px);
          }
        `;
        
      case 'outlined':
        return css`
          border: 2px solid ${theme.colors.border};
          box-shadow: none;
          
          &:hover {
            border-color: ${theme.colors.primary[500]};
            box-shadow: ${theme.boxShadow.md};
          }
        `;
        
      case 'gradient':
        const gradientColor = gradient ? theme.colors.gradients[gradient] : theme.colors.gradients.primary;
        return css`
          background: ${gradientColor};
          border: none;
          color: white;
          
          .ant-card-head-title {
            color: white;
          }
          
          .ant-card-extra {
            color: white;
          }
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: ${theme.boxShadow.xl};
            filter: brightness(1.05);
          }
        `;
        
      case 'glass':
        return css`
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          
          &:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
          }
        `;
        
      default:
        return css`
          border: 1px solid ${theme.colors.borderLight};
          box-shadow: ${theme.boxShadow.sm};
          
          &:hover {
            box-shadow: ${theme.boxShadow.md};
          }
        `;
    }
  }}

  // 悬停效果
  ${({ hoverable }) => hoverable && css`
    cursor: pointer;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: ${theme.boxShadow.lg};
    }
    
    &:active {
      transform: translateY(-1px);
    }
  `}

  // 动画效果
  ${({ animated }) => animated && css`
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s;
    }
    
    &:hover::before {
      left: 100%;
    }
  `}

  // 发光效果
  ${({ glowing }) => glowing && css`
    &::after {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: ${theme.colors.gradients.primary};
      border-radius: ${theme.borderRadius.lg};
      z-index: -1;
      opacity: 0;
      transition: opacity ${theme.animation.duration.base};
    }
    
    &:hover::after {
      opacity: 0.7;
    }
  `}

  // 卡片头部样式
  .ant-card-head {
    border-bottom: 1px solid ${theme.colors.borderLight};
    
    .ant-card-head-title {
      font-weight: ${theme.typography.fontWeight.semibold};
      font-size: ${theme.typography.fontSize.lg};
    }
  }

  // 卡片内容样式
  .ant-card-body {
    position: relative;
  }

  // 卡片操作区域
  .ant-card-actions {
    border-top: 1px solid ${theme.colors.borderLight};
    
    > li {
      margin: 12px 0;
      
      &:not(:last-child) {
        border-right: 1px solid ${theme.colors.borderLight};
      }
    }
  }

  // 加载状态
  &.ant-card-loading {
    .ant-card-body {
      user-select: none;
    }
  }
`;

const Card: React.FC<CustomCardProps> = ({
  variant = 'default',
  size = 'medium',
  gradient,
  hoverable = false,
  animated = false,
  glowing = false,
  children,
  ...props
}) => {
  return (
    <StyledCard
      variant={variant}
      size={size}
      gradient={gradient}
      hoverable={hoverable}
      animated={animated}
      glowing={glowing}
      {...props}
    >
      {children}
    </StyledCard>
  );
};

export default Card;
